"""
Admin layout components without sidebar.
"""
import flet as ft
from gui.config.constants import ROUTE_ADMIN_DASHBOARD, ROUTE_ADMIN_TEACHERS, ROUTE_LOGIN
from gui.config.language import get_text, DEFAULT_LANGUAGE

def create_admin_app_bar(page: ft.Page, title: str):
    """Create admin app bar with navigation."""
    current_user = getattr(page.app_state, 'current_user', None)
    current_language = getattr(page, 'language', DEFAULT_LANGUAGE)

    def logout_click(_):
        """Handle logout."""
        page.session.remove("current_user")
        page.app_state.current_user = None
        page.go(ROUTE_LOGIN)

    def dashboard_click(_):
        """Navigate to admin dashboard."""
        page.go(ROUTE_ADMIN_DASHBOARD)

    def teachers_click(_):
        """Navigate to teacher management."""
        page.go(ROUTE_ADMIN_TEACHERS)

    def data_click(_):
        """Navigate to data overview."""
        page.go("/admin/data")

    def subjects_click(_):
        """Navigate to subjects management."""
        page.go("/admin/subjects")

    def classes_click(_):
        """Navigate to classes management."""
        page.go("/admin/classes")

    # Admin navigation buttons with French text and modern styling
    nav_buttons = ft.Row([
        ft.Container(
            content=ft.TextButton(
                get_text("dashboard", current_language),
                icon=ft.Icons.DASHBOARD,
                style=ft.ButtonStyle(
                    color=ft.Colors.WHITE if page.route == ROUTE_ADMIN_DASHBOARD else ft.Colors.BLUE_600,
                    bgcolor=ft.Colors.BLUE_600 if page.route == ROUTE_ADMIN_DASHBOARD else ft.Colors.TRANSPARENT,
                    text_style=ft.TextStyle(
                        weight=ft.FontWeight.W_600,
                        size=14
                    ),
                    shape=ft.RoundedRectangleBorder(radius=8),
                    padding=ft.padding.symmetric(horizontal=16, vertical=8)
                ),
                on_click=dashboard_click
            ),
            margin=ft.margin.symmetric(horizontal=2)
        ),
        ft.Container(
            content=ft.TextButton(
                "Enseignants",
                icon=ft.Icons.PEOPLE,
                style=ft.ButtonStyle(
                    color=ft.Colors.WHITE if page.route == ROUTE_ADMIN_TEACHERS else ft.Colors.BLUE_600,
                    bgcolor=ft.Colors.BLUE_600 if page.route == ROUTE_ADMIN_TEACHERS else ft.Colors.TRANSPARENT,
                    text_style=ft.TextStyle(
                        weight=ft.FontWeight.W_600,
                        size=14
                    ),
                    shape=ft.RoundedRectangleBorder(radius=8),
                    padding=ft.padding.symmetric(horizontal=16, vertical=8)
                ),
                on_click=teachers_click
            ),
            margin=ft.margin.symmetric(horizontal=2)
        ),
        
        ft.Container(
            content=ft.TextButton(
                "Matières",
                icon=ft.Icons.BOOK,
                style=ft.ButtonStyle(
                    color=ft.Colors.WHITE if page.route == "/admin/subjects" else ft.Colors.BLUE_600,
                    bgcolor=ft.Colors.BLUE_600 if page.route == "/admin/subjects" else ft.Colors.TRANSPARENT,
                    text_style=ft.TextStyle(
                        weight=ft.FontWeight.W_600,
                        size=14
                    ),
                    shape=ft.RoundedRectangleBorder(radius=8),
                    padding=ft.padding.symmetric(horizontal=16, vertical=8)
                ),
                on_click=subjects_click
            ),
            margin=ft.margin.symmetric(horizontal=2)
        ),
        ft.Container(
            content=ft.TextButton(
                "Classes",
                icon=ft.Icons.CLASS_,
                style=ft.ButtonStyle(
                    color=ft.Colors.WHITE if page.route == "/admin/classes" else ft.Colors.BLUE_600,
                    bgcolor=ft.Colors.BLUE_600 if page.route == "/admin/classes" else ft.Colors.TRANSPARENT,
                    text_style=ft.TextStyle(
                        weight=ft.FontWeight.W_600,
                        size=14
                    ),
                    shape=ft.RoundedRectangleBorder(radius=8),
                    padding=ft.padding.symmetric(horizontal=16, vertical=8)
                ),
                on_click=classes_click
            ),
            margin=ft.margin.symmetric(horizontal=2)
        ),
        ft.Container(
            content=ft.TextButton(
                "Données",
                icon=ft.Icons.ANALYTICS,
                style=ft.ButtonStyle(
                    color=ft.Colors.WHITE if page.route == "/admin/data" else ft.Colors.BLUE_600,
                    bgcolor=ft.Colors.BLUE_600 if page.route == "/admin/data" else ft.Colors.TRANSPARENT,
                    text_style=ft.TextStyle(
                        weight=ft.FontWeight.W_600,
                        size=14
                    ),
                    shape=ft.RoundedRectangleBorder(radius=8),
                    padding=ft.padding.symmetric(horizontal=16, vertical=8)
                ),
                on_click=data_click
            ),
            margin=ft.margin.symmetric(horizontal=2)
        ),

    ], spacing=5)

    # User info and logout with French text
    user_section = ft.Row([
        ft.Container(
            content=ft.Text(
                f"Administrateur: {current_user.get('full_name', 'Admin') if current_user else 'Admin'}",
                size=13,
                color=ft.Colors.BLUE_GREY_700,
                weight=ft.FontWeight.W_500
            ),
            padding=ft.padding.symmetric(horizontal=8, vertical=4),
            bgcolor=ft.Colors.BLUE_50,
            border_radius=6
        ),
        ft.Container(
            content=ft.IconButton(
                icon=ft.Icons.LOGOUT,
                tooltip="Déconnexion",
                icon_color=ft.Colors.WHITE,
                bgcolor=ft.Colors.RED_500,
                icon_size=18,
                on_click=logout_click
            ),
            margin=ft.margin.only(left=8)
        )
    ], spacing=0)

    return ft.AppBar(
        title=ft.Text(
            title,
            size=22,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.BLUE_900
        ),
        center_title=False,
        bgcolor=ft.Colors.BLUE_50,
        elevation=2,
        shadow_color=ft.Colors.BLUE_100,
        actions=[
            ft.Container(
                content=nav_buttons,
                padding=ft.padding.symmetric(horizontal=12, vertical=4),
                margin=ft.margin.only(right=10)
            ),
            ft.Container(
                content=user_section,
                padding=ft.padding.symmetric(horizontal=12, vertical=4),
                margin=ft.margin.only(right=10)
            )
        ]
    )

def create_admin_page_layout(page: ft.Page, title: str, content):
    """
    Create an admin page layout without sidebar.

    Args:
        page: The Flet page object
        title: The page title
        content: The page content

    Returns:
        ft.View: The page view
    """
    # Create admin app bar
    app_bar = create_admin_app_bar(page, title)

    # Create content column
    is_mobile = getattr(page, 'is_mobile', False)

    content_column = []

    if isinstance(content, list):
        content_column.extend(content)
    else:
        content_column.append(content)

    # Create main content area with scroll and modern styling
    main_content = ft.Container(
        content=ft.Column(
            content_column,
            expand=True,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            alignment=ft.MainAxisAlignment.START,
            scroll=ft.ScrollMode.AUTO,
            spacing=20
        ),
        expand=True,
        padding=ft.padding.all(24 if not is_mobile else 16),
        bgcolor=ft.Colors.GREY_50,
        # Add subtle gradient background
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_center,
            end=ft.alignment.bottom_center,
            colors=[ft.Colors.BLUE_50, ft.Colors.GREY_50, ft.Colors.WHITE]
        )
    )

    # Create the layout
    return ft.View(
        page.route,
        [
            app_bar,
            main_content
        ],
        padding=0,
        spacing=0,
        bgcolor=ft.Colors.WHITE
    )
