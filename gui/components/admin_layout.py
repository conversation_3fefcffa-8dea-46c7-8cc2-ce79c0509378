"""
Admin layout components without sidebar.
"""
import flet as ft
from gui.config.constants import ROUTE_ADMIN_DASHBOARD, ROUTE_ADMIN_TEACHERS, ROUTE_LOGIN
from gui.config.language import get_text, DEFAULT_LANGUAGE

def create_admin_app_bar(page: ft.Page, title: str):
    """Create admin app bar with navigation."""
    current_user = getattr(page.app_state, 'current_user', None)
    current_language = getattr(page, 'language', DEFAULT_LANGUAGE)

    def logout_click(_):
        """Handle logout."""
        page.session.remove("current_user")
        page.app_state.current_user = None
        page.go(ROUTE_LOGIN)

    def dashboard_click(_):
        """Navigate to admin dashboard."""
        page.go(ROUTE_ADMIN_DASHBOARD)

    def teachers_click(_):
        """Navigate to teacher management."""
        page.go(ROUTE_ADMIN_TEACHERS)



    # Simplified admin navigation with only essential buttons
    nav_buttons = ft.Row([
        ft.Container(
            content=ft.TextButton(
                "Tableau de Bord",
                icon=ft.Icons.DASHBOARD,
                style=ft.ButtonStyle(
                    color=ft.Colors.WHITE if page.route == ROUTE_ADMIN_DASHBOARD else ft.Colors.BLUE_600,
                    bgcolor=ft.Colors.BLUE_600 if page.route == ROUTE_ADMIN_DASHBOARD else ft.Colors.TRANSPARENT,
                    text_style=ft.TextStyle(weight=ft.FontWeight.W_500, size=14),
                    shape=ft.RoundedRectangleBorder(radius=6),
                    padding=ft.padding.symmetric(horizontal=12, vertical=6)
                ),
                on_click=dashboard_click
            ),
            margin=ft.margin.symmetric(horizontal=4)
        ),
        ft.Container(
            content=ft.TextButton(
                "Enseignants",
                icon=ft.Icons.PEOPLE,
                style=ft.ButtonStyle(
                    color=ft.Colors.WHITE if page.route == ROUTE_ADMIN_TEACHERS else ft.Colors.BLUE_600,
                    bgcolor=ft.Colors.BLUE_600 if page.route == ROUTE_ADMIN_TEACHERS else ft.Colors.TRANSPARENT,
                    text_style=ft.TextStyle(weight=ft.FontWeight.W_500, size=14),
                    shape=ft.RoundedRectangleBorder(radius=6),
                    padding=ft.padding.symmetric(horizontal=12, vertical=6)
                ),
                on_click=teachers_click
            ),
            margin=ft.margin.symmetric(horizontal=4)
        ),
    ], spacing=8)

    # User info and logout with French text
    user_section = ft.Row([
        ft.Container(
            content=ft.Text(
                f"Administrateur: {current_user.get('full_name', 'Admin') if current_user else 'Admin'}",
                size=13,
                color=ft.Colors.BLUE_GREY_700,
                weight=ft.FontWeight.W_500
            ),
            padding=ft.padding.symmetric(horizontal=8, vertical=4),
            bgcolor=ft.Colors.BLUE_50,
            border_radius=6
        ),
        ft.Container(
            content=ft.IconButton(
                icon=ft.Icons.LOGOUT,
                tooltip="Déconnexion",
                icon_color=ft.Colors.WHITE,
                bgcolor=ft.Colors.RED_500,
                icon_size=18,
                on_click=logout_click
            ),
            margin=ft.margin.only(left=8)
        )
    ], spacing=0)

    return ft.AppBar(
        title=ft.Text(
            title,
            size=20,
            weight=ft.FontWeight.W_600,
            color=ft.Colors.BLUE_800
        ),
        center_title=False,
        bgcolor=ft.Colors.GREY_50,
        elevation=1,
        actions=[
            ft.Container(
                content=nav_buttons,
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                margin=ft.margin.only(right=8)
            ),
            ft.Container(
                content=user_section,
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                margin=ft.margin.only(right=8)
            )
        ]
    )

def create_admin_page_layout(page: ft.Page, title: str, content):
    """
    Create an admin page layout without sidebar.

    Args:
        page: The Flet page object
        title: The page title
        content: The page content

    Returns:
        ft.View: The page view
    """
    # Create admin app bar
    app_bar = create_admin_app_bar(page, title)

    # Create content column
    is_mobile = getattr(page, 'is_mobile', False)

    content_column = []

    if isinstance(content, list):
        content_column.extend(content)
    else:
        content_column.append(content)

    # Create main content area with simple, clean styling
    main_content = ft.Container(
        content=ft.Column(
            content_column,
            expand=True,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            alignment=ft.MainAxisAlignment.START,
            scroll=ft.ScrollMode.AUTO,
            spacing=16
        ),
        expand=True,
        padding=ft.padding.all(20 if not is_mobile else 12),
        bgcolor=ft.Colors.WHITE
    )

    # Create the layout
    return ft.View(
        page.route,
        [
            app_bar,
            main_content
        ],
        padding=0,
        spacing=0,
        bgcolor=ft.Colors.WHITE
    )
