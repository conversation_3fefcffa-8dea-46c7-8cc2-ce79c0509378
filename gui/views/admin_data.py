"""
Admin data overview view showing all system data.
"""
import flet as ft
import sqlite3
from gui.components.admin_layout import create_admin_page_layout
from facial_recognition_system.config import Config
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_DASHBOARD
from gui.config.language import get_text

def create_admin_data_view(page: ft.Page):
    """Create the admin data overview view."""
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/data", controls=[])

    def get_all_data():
        """Get all data from the database."""
        try:
            with sqlite3.connect(Config.get_db_path()) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                data = {}

                # Get classes with teacher names
                cursor.execute("""
                    SELECT c.*, t.full_name as teacher_name
                    FROM classes c
                    LEFT JOIN users u ON c.teacher_id = u.id
                    LEFT JOIN teachers t ON u.id = t.user_id
                    ORDER BY c.name
                """)
                data['classes'] = [dict(row) for row in cursor.fetchall()]

                # Get students
                cursor.execute("""
                    SELECT e.*, c.name as class_name
                    FROM etudiants e
                    LEFT JOIN classes c ON e.class_id = c.id
                    ORDER BY e.name
                """)
                data['students'] = [dict(row) for row in cursor.fetchall()]

                # Get subjects
                cursor.execute("""
                    SELECT m.*, c.name as class_name
                    FROM matieres m
                    LEFT JOIN classes c ON m.class_id = c.id
                    ORDER BY m.name
                """)
                data['subjects'] = [dict(row) for row in cursor.fetchall()]

                # Get quizzes
                cursor.execute("""
                    SELECT q.*, c.name as class_name, m.name as subject_name
                    FROM quiz q
                    LEFT JOIN classes c ON q.class_id = c.id
                    LEFT JOIN matieres m ON q.subject_id = m.id
                    ORDER BY q.created_at DESC
                """)
                data['quizzes'] = [dict(row) for row in cursor.fetchall()]

                # Get attendance records
                cursor.execute("""
                    SELECT p.*, e.name as student_name, c.name as class_name, m.name as subject_name
                    FROM presences p
                    LEFT JOIN etudiants e ON p.student_id = e.id
                    LEFT JOIN classes c ON p.class_id = c.id
                    LEFT JOIN matieres m ON p.subject_id = m.id
                    ORDER BY p.date DESC, p.time DESC
                    LIMIT 100
                """)
                data['attendance'] = [dict(row) for row in cursor.fetchall()]

                return data

        except Exception as e:
            print(f"❌ Failed to get data: {e}")
            return {}

    # Get all data
    all_data = get_all_data()

    # Simple welcome section
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Text(
                "Aperçu des Données",
                size=24,
                weight=ft.FontWeight.W_600,
                color=ft.Colors.BLUE_800,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                "Données principales du système",
                size=14,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER
            )
        ],
        spacing=8,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20),
        bgcolor=ft.Colors.GREY_50,
        border_radius=ft.border_radius.all(12),
        alignment=ft.alignment.center,
    )

    def create_data_table(title: str, data: list, columns: list, icon: str = ft.Icons.TABLE_CHART):
        """Create a simple data table for a specific data type."""
        if not data:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.INBOX, size=32, color=ft.Colors.GREY_400),
                    ft.Text(f"Aucune donnée {title.lower()}", size=16, color=ft.Colors.GREY_600)
                ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                alignment=ft.alignment.center,
                padding=ft.padding.all(24),
                bgcolor=ft.Colors.GREY_50,
                border_radius=ft.border_radius.all(8),
                margin=ft.margin.only(bottom=16)
            )

        # Create simple table rows
        rows = []
        for item in data[:20]:  # Limit to 20 items for better performance
            cells = []
            for col in columns:
                value = str(item.get(col, ''))
                if len(value) > 25:
                    value = value[:22] + "..."
                cells.append(ft.DataCell(ft.Text(value, size=12)))
            rows.append(ft.DataRow(cells=cells))

        # Create table columns
        table_columns = [
            ft.DataColumn(ft.Text(col.replace('_', ' ').title(), weight=ft.FontWeight.W_500, size=13))
            for col in columns
        ]

        return ft.Container(
            content=ft.Column([
                ft.Text(title, size=16, weight=ft.FontWeight.W_600, color=ft.Colors.BLUE_800),
                ft.Text(f"{len(data)} élément{'s' if len(data) > 1 else ''}", size=12, color=ft.Colors.GREY_600),
                ft.DataTable(
                    columns=table_columns,
                    rows=rows,
                    border=ft.border.all(1, ft.Colors.GREY_300),
                    border_radius=6,
                    heading_row_color=ft.Colors.GREY_100,
                    show_checkbox_column=False,
                )
            ], spacing=8),
            padding=ft.padding.all(16),
            margin=ft.margin.only(bottom=16),
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.all(8),
            border=ft.border.all(1, ft.Colors.GREY_200)
        )

    # Create simplified data sections with essential information only
    data_sections = []
    data_sections.append(create_data_table("Classes", all_data.get('classes', []),
                         ['name', 'teacher_name'], ft.Icons.SCHOOL))
    data_sections.append(create_data_table("Étudiants", all_data.get('students', []),
                         ['name', 'class_name'], ft.Icons.PEOPLE))
    data_sections.append(create_data_table("Présences Récentes", all_data.get('attendance', [])[:10],  # Limit to 10 recent entries
                         ['student_name', 'class_name', 'status', 'date'], ft.Icons.CHECK_CIRCLE))

    # Create simple content layout
    content = [welcome_section] + data_sections

    return create_admin_page_layout(
        page,
        "Aperçu des Données",
        content
    )
