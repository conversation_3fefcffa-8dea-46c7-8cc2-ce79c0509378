"""
Admin dashboard view for managing teachers and viewing system statistics.
"""
import flet as ft
from gui.components.admin_layout import create_admin_page_layout
from gui.services.auth_service import AuthService
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_TEACHERS
from gui.config.language import get_text

def create_admin_dashboard_view(page: ft.Page):
    """Create the admin dashboard with statistics and teacher management."""
    auth_service = AuthService()
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin", controls=[])

    # Get statistics
    stats = auth_service.get_admin_statistics()

    # Simple welcome section
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Text(
                f"Bienvenue, {current_user.get('full_name', 'Admin')}",
                size=22,
                weight=ft.FontWeight.W_600,
                color=ft.Colors.BLUE_800,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                "Tableau de Bord Administrateur",
                size=14,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER
            )
        ],
        spacing=6,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(16),
        margin=ft.margin.only(bottom=16),
        bgcolor=ft.Colors.GREY_50,
        border_radius=ft.border_radius.all(8),
        alignment=ft.alignment.center,
    )

    # Statistics cards with modern design
    def create_stat_card(title: str, value: int, icon: str, color: str, route: str = None):
        """Create a clickable statistics card."""
        card_width = page.width*0.85 if is_mobile else 280

        def on_card_click(_):
            if route:
                page.go(route)

        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Container(
                        content=ft.Icon(icon, size=28, color=ft.Colors.WHITE),
                        bgcolor=color,
                        padding=ft.padding.all(12),
                        border_radius=ft.border_radius.all(12)
                    ),
                    ft.Column([
                        ft.Text(
                            title,
                            size=16,
                            weight=ft.FontWeight.W_500,
                            color=ft.Colors.BLUE_GREY_200
                        ),
                        ft.Text(
                            str(value),
                            size=32,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.ON_SURFACE
                        )
                    ], spacing=4, alignment=ft.MainAxisAlignment.CENTER, expand=True)
                ], alignment=ft.MainAxisAlignment.START, spacing=16),
                # Add click indicator
                ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.Icons.ARROW_FORWARD_IOS, size=12, color=ft.Colors.BLUE_GREY_400),
                        ft.Text("Cliquer pour voir", size=10, color=ft.Colors.BLUE_GREY_400)
                    ], alignment=ft.MainAxisAlignment.END),
                    margin=ft.margin.only(top=8)
                ) if route else ft.Container()
            ], spacing=0),
            width=card_width,
            padding=ft.padding.all(24),
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.all(8),
            border=ft.border.all(1, ft.Colors.GREY_200),
            margin=ft.margin.all(6),
            on_click=on_card_click if route else None,
            ink=True if route else False
        )

    # Create simplified statistics cards
    stats_cards = []
    stats_cards.append(create_stat_card("Enseignants", stats.get('total_teachers', 0), ft.Icons.PERSON, ft.Colors.BLUE_600, ROUTE_ADMIN_TEACHERS))
    stats_cards.append(create_stat_card("Classes", stats.get('total_classes', 0), ft.Icons.SCHOOL, ft.Colors.GREEN_600, None))
    stats_cards.append(create_stat_card("Matières", stats.get('total_subjects', 0), ft.Icons.BOOK, ft.Colors.PURPLE_600, None))

    # Simple layout for cards
    if is_mobile:
        cards_layout = ft.Column(stats_cards, alignment=ft.MainAxisAlignment.CENTER, spacing=12)
    else:
        cards_layout = ft.Row(stats_cards, alignment=ft.MainAxisAlignment.CENTER, spacing=16, wrap=True)

    # Create simplified dashboard content
    content = [
        welcome_section,
        cards_layout
    ]

    return create_admin_page_layout(
        page,
        "Tableau de Bord Admin",
        content
    )

