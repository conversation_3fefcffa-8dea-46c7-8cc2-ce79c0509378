"""
Individual data detail views for admin.
"""
import flet as ft
import sqlite3
from gui.components.admin_layout import create_admin_page_layout
from facial_recognition_system.config import Config
from gui.config.constants import ROUTE_LOGIN

def create_data_table(title: str, data: list, columns: list, icon: str = ft.Icons.TABLE_CHART, page=None):
    """Create a modern data table for a specific data type."""
    is_mobile = getattr(page, 'is_mobile', False) if page else False

    if not data:
        return ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Icon(
                        ft.Icons.INBOX,
                        size=48,
                        color=ft.Colors.BLUE_300
                    ),
                    width=96,
                    height=96,
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=48,
                    alignment=ft.alignment.center
                ),
                ft.Text(
                    f"Aucune donnée {title.lower()}",
                    size=18,
                    color=ft.Colors.BLUE_900,
                    text_align=ft.TextAlign.CENTER,
                    weight=ft.FontWeight.BOLD
                ),
                ft.Text(
                    f"Les données {title.lower()} apparaîtront ici",
                    size=14,
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                )
            ], spacing=16, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            alignment=ft.alignment.center,
            padding=ft.padding.all(48),
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.all(20),
            margin=ft.margin.only(bottom=24),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLUE_600),
                offset=ft.Offset(0, 4)
            ),
            border=ft.border.all(1, ft.Colors.BLUE_100)
        )

    # Create table rows with modern styling
    rows = []
    for i, item in enumerate(data):
        cells = []
        for col in columns:
            value = str(item.get(col, '') or 'Non assigné')

            # Special formatting for specific columns
            if col == 'teacher_name' and (not item.get(col) or item.get(col) == 'None'):
                value = 'Non assigné'
                text_color = ft.Colors.GREY_500
                text_style = ft.FontWeight.W_400
            elif col == 'teacher_name':
                text_color = ft.Colors.GREEN_700
                text_style = ft.FontWeight.W_500
            elif col == 'name':
                text_color = ft.Colors.BLUE_800
                text_style = ft.FontWeight.W_600
            else:
                text_color = ft.Colors.GREY_800
                text_style = ft.FontWeight.W_400

            if len(value) > 30:
                value = value[:27] + "..."

            cells.append(ft.DataCell(
                ft.Text(
                    value,
                    size=13,
                    color=text_color,
                    weight=text_style
                )
            ))

        # Alternate row colors for better readability
        row_color = ft.Colors.BLUE_50 if i % 2 == 0 else ft.Colors.WHITE
        rows.append(ft.DataRow(cells=cells, color=row_color))

    # Create table columns with modern styling and French labels
    column_labels = {
        'name': 'Nom',
        'teacher_name': 'Enseignant',
        'description': 'Description',
        'created_at': 'Date de création',
        'class_name': 'Classe',
        'subject_name': 'Matière',
        'title': 'Titre',
        'status': 'Statut',
        'date': 'Date',
        'time': 'Heure',
        'student_name': 'Étudiant'
    }

    table_columns = [
        ft.DataColumn(
            ft.Text(
                column_labels.get(col, col.replace('_', ' ').title()),
                weight=ft.FontWeight.BOLD,
                size=14,
                color=ft.Colors.WHITE
            )
        ) for col in columns
    ]

    return ft.Container(
        content=ft.Column([
            # Header with icon and title
            ft.Row([
                ft.Container(
                    content=ft.Icon(
                        icon,
                        size=24,
                        color=ft.Colors.BLUE_600
                    ),
                    width=40,
                    height=40,
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=20,
                    alignment=ft.alignment.center
                ),
                ft.Column([
                    ft.Text(
                        title,
                        size=20,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.BLUE_900
                    ),
                    ft.Text(
                        f"{len(data)} élément{'s' if len(data) > 1 else ''} au total",
                        size=14,
                        color=ft.Colors.GREY_600
                    )
                ], spacing=2, expand=True)
            ], spacing=12, alignment=ft.MainAxisAlignment.START),

            ft.Container(height=16),

            # Modern data table
            ft.Container(
                content=ft.DataTable(
                    columns=table_columns,
                    rows=rows,
                    border=ft.border.all(1, ft.Colors.BLUE_100),
                    border_radius=12,
                    vertical_lines=ft.BorderSide(1, ft.Colors.BLUE_50),
                    horizontal_lines=ft.BorderSide(1, ft.Colors.BLUE_50),
                    heading_row_color=ft.Colors.BLUE_600,
                    heading_row_height=56,
                    show_checkbox_column=False,
                ),
                bgcolor=ft.Colors.WHITE,
                border_radius=12,
                padding=ft.padding.all(16),
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=8,
                    color=ft.Colors.with_opacity(0.1, ft.Colors.BLUE_600),
                    offset=ft.Offset(0, 4)
                ),
            )
        ], spacing=0),
        width=page.width*0.95 if is_mobile else None,
        padding=ft.padding.all(24),
        margin=ft.margin.only(bottom=24),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(20),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=12,
            color=ft.Colors.with_opacity(0.08, ft.Colors.BLUE_600),
            offset=ft.Offset(0, 6)
        ),
        border=ft.border.all(1, ft.Colors.BLUE_100)
    )

def get_data_by_type(data_type: str):
    """Get specific data from the database."""
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            if data_type == 'classes':
                cursor.execute("""
                    SELECT c.*, t.full_name as teacher_name
                    FROM classes c
                    LEFT JOIN users u ON c.teacher_id = u.id
                    LEFT JOIN teachers t ON u.id = t.user_id
                    ORDER BY c.name
                """)
                return [dict(row) for row in cursor.fetchall()]

            elif data_type == 'students':
                cursor.execute("""
                    SELECT e.*, c.name as class_name
                    FROM etudiants e
                    LEFT JOIN classes c ON e.class_id = c.id
                    ORDER BY e.name
                """)
                return [dict(row) for row in cursor.fetchall()]

            elif data_type == 'subjects':
                cursor.execute("""
                    SELECT m.*, c.name as class_name
                    FROM matieres m
                    LEFT JOIN classes c ON m.class_id = c.id
                    ORDER BY m.name
                """)
                return [dict(row) for row in cursor.fetchall()]

            elif data_type == 'quizzes':
                cursor.execute("""
                    SELECT q.*, c.name as class_name, m.name as subject_name
                    FROM quiz q
                    LEFT JOIN classes c ON q.class_id = c.id
                    LEFT JOIN matieres m ON q.subject_id = m.id
                    ORDER BY q.created_at DESC
                """)
                return [dict(row) for row in cursor.fetchall()]

            elif data_type == 'attendance':
                cursor.execute("""
                    SELECT p.*, e.name as student_name, c.name as class_name, m.name as subject_name
                    FROM presences p
                    LEFT JOIN etudiants e ON p.student_id = e.id
                    LEFT JOIN classes c ON p.class_id = c.id
                    LEFT JOIN matieres m ON p.subject_id = m.id
                    ORDER BY p.date DESC, p.time DESC
                    LIMIT 200
                """)
                return [dict(row) for row in cursor.fetchall()]

            return []

    except Exception as e:
        print(f"❌ Failed to get {data_type} data: {e}")
        return []

def create_search_section(on_search_change, page):
    """Create the search section for classes."""
    is_mobile = getattr(page, 'is_mobile', False)

    # Summary text (will be updated dynamically)
    summary_text = ft.Text(
        "",
        size=14,
        color=ft.Colors.GREY_600,
        text_align=ft.TextAlign.CENTER
    )
    page._classes_summary = summary_text  # Store reference for updates

    return ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.SEARCH, size=24, color=ft.Colors.BLUE_600),
                ft.Text(
                    "Rechercher des Classes",
                    size=18,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.WHITE
                )
            ], spacing=12, alignment=ft.MainAxisAlignment.CENTER),
            ft.TextField(
                hint_text="Rechercher par nom de classe, enseignant ou description...",
                border_radius=12,
                content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
                border_color=ft.Colors.WHITE30,
                focused_border_color=ft.Colors.WHITE,
                prefix_icon=ft.Icons.SEARCH,
                on_change=on_search_change,
                width=page.width*0.8 if is_mobile else 500,
                color=ft.Colors.WHITE,
                cursor_color=ft.Colors.WHITE,
                bgcolor=ft.Colors.with_opacity(0.1, ft.Colors.WHITE)
            ),
            summary_text
        ], spacing=12, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(28),
        margin=ft.margin.only(bottom=24),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_700, ft.Colors.INDIGO_600, ft.Colors.PURPLE_600]
        ),
        border_radius=ft.border_radius.all(24),
        alignment=ft.alignment.center,
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=20,
            color=ft.Colors.with_opacity(0.3, ft.Colors.BLUE_600),
            offset=ft.Offset(0, 8)
        )
    )

def create_teacher_classes_section(teacher_name, classes, page):
    """Create a section showing classes for a specific teacher."""
    # Determine colors based on teacher assignment
    if teacher_name == 'Non assigné':
        header_color = ft.Colors.ORANGE_600
        bg_color = ft.Colors.ORANGE_50
        border_color = ft.Colors.ORANGE_200
        icon = ft.Icons.WARNING_OUTLINED
    else:
        header_color = ft.Colors.GREEN_600
        bg_color = ft.Colors.GREEN_50
        border_color = ft.Colors.GREEN_200
        icon = ft.Icons.PERSON

    # Create class cards for this teacher
    class_cards = []
    for cls in classes:
        class_card = create_class_card_compact(cls, page)
        class_cards.append(class_card)

    return ft.Container(
        content=ft.Column([
            # Teacher header
            ft.Row([
                ft.Container(
                    content=ft.Icon(
                        icon,
                        size=20,
                        color=header_color
                    ),
                    width=36,
                    height=36,
                    bgcolor=bg_color,
                    border_radius=18,
                    alignment=ft.alignment.center
                ),
                ft.Column([
                    ft.Text(
                        teacher_name,
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=header_color
                    ),
                    ft.Text(
                        f"{len(classes)} classe{'s' if len(classes) > 1 else ''}",
                        size=14,
                        color=ft.Colors.GREY_600
                    )
                ], spacing=2, expand=True)
            ], spacing=12, alignment=ft.MainAxisAlignment.START),

            ft.Container(height=8),

            # Classes grid/list
            ft.Column(
                controls=class_cards,
                spacing=8
            ) if class_cards else ft.Container(
                content=ft.Text(
                    "Aucune classe trouvée",
                    size=14,
                    color=ft.Colors.GREY_500,
                    italic=True
                ),
                padding=ft.padding.all(16),
                alignment=ft.alignment.center
            )
        ], spacing=0),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=16),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(16),
        border=ft.border.all(1, border_color),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.1, header_color),
            offset=ft.Offset(0, 4)
        )
    )

def create_class_card_compact(class_data, page):
    """Create a compact card for a single class."""
    # Format creation date
    created_at = class_data.get('created_at', '')
    if created_at:
        try:
            from datetime import datetime
            dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            formatted_date = dt.strftime('%d/%m/%Y')
        except:
            formatted_date = created_at[:10] if len(created_at) >= 10 else created_at
    else:
        formatted_date = 'N/A'

    # Get description
    description = class_data.get('description', '') or 'Aucune description'
    if len(description) > 60:
        description = description[:57] + "..."

    return ft.Container(
        content=ft.Row([
            # Class info
            ft.Column([
                ft.Text(
                    class_data['name'],
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_800
                ),
                ft.Text(
                    description,
                    size=13,
                    color=ft.Colors.GREY_700,
                    max_lines=2,
                    overflow=ft.TextOverflow.ELLIPSIS
                ),
                ft.Text(
                    f"Créée le {formatted_date}",
                    size=12,
                    color=ft.Colors.GREY_500
                )
            ], spacing=4, expand=True),

            # Class ID badge
            ft.Container(
                content=ft.Text(
                    f"ID: {class_data.get('id', 'N/A')}",
                    size=11,
                    color=ft.Colors.BLUE_600,
                    weight=ft.FontWeight.W_500
                ),
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                bgcolor=ft.Colors.BLUE_50,
                border_radius=8,
                border=ft.border.all(1, ft.Colors.BLUE_200)
            )
        ], spacing=12, alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
        padding=ft.padding.all(16),
        bgcolor=ft.Colors.GREY_50,
        border_radius=12,
        border=ft.border.all(1, ft.Colors.GREY_200),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=4,
            color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
            offset=ft.Offset(0, 2)
        )
    )

def create_overview_tab(classes_data, students_data, page):
    """Create the overview tab with statistics and quick insights."""
    # Calculate statistics
    total_classes = len(classes_data)
    total_students = len(students_data)

    # Group classes by teacher for stats
    classes_by_teacher = {}
    unassigned_classes = 0
    for cls in classes_data:
        teacher_name = cls.get('teacher_name')
        if teacher_name:
            if teacher_name not in classes_by_teacher:
                classes_by_teacher[teacher_name] = 0
            classes_by_teacher[teacher_name] += 1
        else:
            unassigned_classes += 1

    total_teachers = len(classes_by_teacher)

    # Students by class stats
    students_by_class = {}
    unassigned_students = 0
    for student in students_data:
        class_name = student.get('class_name')
        if class_name:
            if class_name not in students_by_class:
                students_by_class[class_name] = 0
            students_by_class[class_name] += 1
        else:
            unassigned_students += 1

    # Create stat cards
    stat_cards = ft.Row([
        create_stat_card("Classes", total_classes, ft.Icons.SCHOOL, ft.Colors.BLUE_600),
        create_stat_card("Étudiants", total_students, ft.Icons.PEOPLE, ft.Colors.GREEN_600),
        create_stat_card("Enseignants", total_teachers, ft.Icons.PERSON, ft.Colors.PURPLE_600),
        create_stat_card("Non assignées", unassigned_classes, ft.Icons.WARNING, ft.Colors.ORANGE_600)
    ], spacing=16, wrap=True, alignment=ft.MainAxisAlignment.CENTER)

    # Quick insights
    insights = []
    if unassigned_classes > 0:
        insights.append(f"⚠️ {unassigned_classes} classe{'s' if unassigned_classes > 1 else ''} sans enseignant assigné")
    if unassigned_students > 0:
        insights.append(f"👥 {unassigned_students} étudiant{'s' if unassigned_students > 1 else ''} sans classe")
    if total_classes > 0 and total_students > 0:
        avg_students = total_students / total_classes
        insights.append(f"📊 Moyenne de {avg_students:.1f} étudiants par classe")

    insights_section = ft.Container(
        content=ft.Column([
            ft.Text("Aperçu Rapide", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_900),
            ft.Column([
                ft.Text(insight, size=14, color=ft.Colors.GREY_700) for insight in insights
            ], spacing=8) if insights else ft.Text("Tout semble en ordre ✅", size=14, color=ft.Colors.GREEN_600)
        ], spacing=12),
        padding=ft.padding.all(20),
        bgcolor=ft.Colors.BLUE_50,
        border_radius=12,
        margin=ft.margin.only(top=20)
    )

    return ft.Container(
        content=ft.Column([
            stat_cards,
            insights_section
        ], spacing=20, expand=True, scroll=ft.ScrollMode.AUTO),
        padding=ft.padding.all(20),
        expand=True
    )

def create_stat_card(title, value, icon, color):
    """Create a statistics card."""
    return ft.Container(
        content=ft.Column([
            ft.Icon(icon, size=32, color=color),
            ft.Text(str(value), size=24, weight=ft.FontWeight.BOLD, color=color),
            ft.Text(title, size=14, color=ft.Colors.GREY_600, text_align=ft.TextAlign.CENTER)
        ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        width=150,
        height=120,
        padding=ft.padding.all(16),
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        border=ft.border.all(1, ft.Colors.with_opacity(0.3, color)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.1, color),
            offset=ft.Offset(0, 4)
        )
    )

def create_classes_tab(all_classes_data, page):
    """Create the classes tab with grouping and search functionality."""
    # State for filtering
    filtered_classes = all_classes_data.copy()
    search_text = ""

    # Create containers for dynamic content
    classes_container = ft.Column(spacing=16)

    def filter_classes(search_query=""):
        """Filter classes based on search text."""
        nonlocal filtered_classes, search_text
        search_text = search_query

        if not search_query:
            filtered_classes = all_classes_data.copy()
        else:
            search_lower = search_query.lower()
            filtered_classes = [
                cls for cls in all_classes_data
                if (search_lower in cls['name'].lower() or
                    search_lower in (cls.get('teacher_name') or '').lower() or
                    search_lower in (cls.get('description') or '').lower())
            ]
        update_classes_display()

    def update_classes_display():
        """Update the classes display with grouping by teacher."""
        classes_container.controls.clear()

        # Group classes by teacher
        classes_by_teacher = {}
        for cls in filtered_classes:
            teacher_name = cls.get('teacher_name') or 'Non assigné'
            if teacher_name not in classes_by_teacher:
                classes_by_teacher[teacher_name] = []
            classes_by_teacher[teacher_name].append(cls)

        # Sort teachers (Non assigné at the end)
        sorted_teachers = sorted(classes_by_teacher.keys(),
                               key=lambda x: (x == 'Non assigné', x.lower()))

        for teacher_name in sorted_teachers:
            teacher_classes = classes_by_teacher[teacher_name]
            teacher_section = create_teacher_classes_section(teacher_name, teacher_classes, page)
            classes_container.controls.append(teacher_section)

        # Add summary
        total_classes = len(filtered_classes)
        total_teachers = len([t for t in classes_by_teacher.keys() if t != 'Non assigné'])
        unassigned_classes = len(classes_by_teacher.get('Non assigné', []))

        summary_text = f"{total_classes} classe{'s' if total_classes > 1 else ''}"
        if search_text:
            summary_text += f" trouvée{'s' if total_classes > 1 else ''}"
        summary_text += f" • {total_teachers} enseignant{'s' if total_teachers > 1 else ''}"
        if unassigned_classes > 0:
            summary_text += f" • {unassigned_classes} non assignée{'s' if unassigned_classes > 1 else ''}"

        # Update summary in search section
        if hasattr(page, '_classes_summary'):
            page._classes_summary.value = summary_text
            page.update()

    def on_search_change(e):
        """Handle search text change."""
        filter_classes(e.control.value)

    # Search section
    search_section = create_search_section(on_search_change, page)

    # Initial display
    update_classes_display()

    return ft.Container(
        content=ft.Column([
            search_section,
            ft.Container(
                content=classes_container,
                expand=True
            )
        ], spacing=0, expand=True, scroll=ft.ScrollMode.AUTO),
        padding=ft.padding.all(0),
        expand=True
    )

def create_students_tab(students_data, page):
    """Create the students tab with enhanced table and search."""
    return ft.Container(
        content=ft.Column([
            create_data_table("Étudiants", students_data, ['name', 'class_name', 'created_at'], ft.Icons.PEOPLE, page)
        ], spacing=0, expand=True, scroll=ft.ScrollMode.AUTO),
        padding=ft.padding.all(20),
        expand=True
    )

def create_admin_classes_view(page: ft.Page):
    """Create the admin classes detail view with custom tab interface."""
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/classes", controls=[])

    # Get all data
    all_classes_data = get_data_by_type('classes')
    students_data = get_data_by_type('students')

    # State for content container
    content_container = ft.Ref[ft.Container]()

    def switch_tab(tab_name):
        """Switch to a different tab."""
        def handler(e):
            # Update content based on tab
            if tab_name == "overview":
                new_content = create_overview_tab(all_classes_data, students_data, page)
            elif tab_name == "classes":
                new_content = create_classes_tab(all_classes_data, page)
            elif tab_name == "students":
                new_content = create_students_tab(students_data, page)
            else:
                return

            # Update content container
            content_container.current.content = new_content

            # Update tab button styles
            for control in tab_buttons_row.controls:
                if hasattr(control, 'content') and hasattr(control.content, 'style'):
                    if control.content.text == get_tab_text(tab_name):
                        # Active tab style
                        control.content.style = ft.ButtonStyle(
                            color=ft.Colors.WHITE,
                            bgcolor=ft.Colors.BLUE_600,
                            text_style=ft.TextStyle(weight=ft.FontWeight.BOLD, size=14),
                            shape=ft.RoundedRectangleBorder(radius=8),
                            padding=ft.padding.symmetric(horizontal=16, vertical=12)
                        )
                    else:
                        # Inactive tab style
                        control.content.style = ft.ButtonStyle(
                            color=ft.Colors.BLUE_600,
                            bgcolor=ft.Colors.TRANSPARENT,
                            text_style=ft.TextStyle(weight=ft.FontWeight.W_500, size=14),
                            shape=ft.RoundedRectangleBorder(radius=8),
                            padding=ft.padding.symmetric(horizontal=16, vertical=12)
                        )

            page.update()

        return handler

    def get_tab_text(tab_name):
        """Get display text for tab."""
        tab_texts = {
            "overview": "Vue d'ensemble",
            "classes": "Classes",
            "students": "Étudiants"
        }
        return tab_texts.get(tab_name, "")

    # Create tab buttons
    tab_buttons_row = ft.Row([
        ft.Container(
            content=ft.TextButton(
                "Vue d'ensemble",
                icon=ft.Icons.DASHBOARD,
                style=ft.ButtonStyle(
                    color=ft.Colors.WHITE,
                    bgcolor=ft.Colors.BLUE_600,
                    text_style=ft.TextStyle(weight=ft.FontWeight.BOLD, size=14),
                    shape=ft.RoundedRectangleBorder(radius=8),
                    padding=ft.padding.symmetric(horizontal=16, vertical=12)
                ),
                on_click=switch_tab("overview")
            )
        ),
        ft.Container(
            content=ft.TextButton(
                "Classes",
                icon=ft.Icons.SCHOOL,
                style=ft.ButtonStyle(
                    color=ft.Colors.BLUE_600,
                    bgcolor=ft.Colors.TRANSPARENT,
                    text_style=ft.TextStyle(weight=ft.FontWeight.W_500, size=14),
                    shape=ft.RoundedRectangleBorder(radius=8),
                    padding=ft.padding.symmetric(horizontal=16, vertical=12)
                ),
                on_click=switch_tab("classes")
            )
        ),
        ft.Container(
            content=ft.TextButton(
                "Étudiants",
                icon=ft.Icons.PEOPLE,
                style=ft.ButtonStyle(
                    color=ft.Colors.BLUE_600,
                    bgcolor=ft.Colors.TRANSPARENT,
                    text_style=ft.TextStyle(weight=ft.FontWeight.W_500, size=14),
                    shape=ft.RoundedRectangleBorder(radius=8),
                    padding=ft.padding.symmetric(horizontal=16, vertical=12)
                ),
                on_click=switch_tab("students")
            )
        )
    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER)

    # Tab header
    tab_header = ft.Container(
        content=tab_buttons_row,
        padding=ft.padding.all(16),
        bgcolor=ft.Colors.GREY_50,
        border_radius=ft.border_radius.only(top_left=16, top_right=16),
        border=ft.border.only(
            left=ft.BorderSide(1, ft.Colors.GREY_200),
            right=ft.BorderSide(1, ft.Colors.GREY_200),
            top=ft.BorderSide(1, ft.Colors.GREY_200)
        )
    )

    # Content container (starts with overview)
    content_container.current = ft.Container(
        content=create_overview_tab(all_classes_data, students_data, page),
        padding=ft.padding.all(0),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.only(bottom_left=16, bottom_right=16),
        border=ft.border.only(
            left=ft.BorderSide(1, ft.Colors.GREY_200),
            right=ft.BorderSide(1, ft.Colors.GREY_200),
            bottom=ft.BorderSide(1, ft.Colors.GREY_200)
        ),
        expand=True
    )

    # Main container
    main_container = ft.Container(
        content=ft.Column([
            tab_header,
            content_container.current
        ], spacing=0, expand=True),
        margin=ft.margin.only(bottom=20),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=12,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 4)
        ),
        expand=True
    )

    content = [main_container]

    return create_admin_page_layout(
        page,
        "Gestion des Classes",
        content
    )

def create_admin_subjects_view(page: ft.Page):
    """Create the admin subjects detail view with quizzes and attendance."""
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/subjects", controls=[])

    # Get subjects, quizzes, and attendance data
    subjects_data = get_data_by_type('subjects')
    quizzes_data = get_data_by_type('quizzes')
    attendance_data = get_data_by_type('attendance')

    content = [
        create_data_table("Matières", subjects_data, ['name', 'class_name', 'description', 'created_at'], ft.Icons.BOOK, page),
        create_data_table("Quiz", quizzes_data, ['title', 'class_name', 'subject_name', 'created_at'], ft.Icons.QUIZ, page),
        create_data_table("Présences", attendance_data, ['student_name', 'class_name', 'subject_name', 'status', 'date', 'time'], ft.Icons.CHECK_CIRCLE, page)
    ]

    return create_admin_page_layout(
        page,
        "Matières, Quiz et Présences",
        content
    )
