"""
Comprehensive admin data view with detailed information.
"""
import flet as ft
import sqlite3
from datetime import datetime, timedelta
from gui.components.admin_layout import create_admin_page_layout
from facial_recognition_system.config import Config
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_DASHBOARD
from gui.config.language import get_text
from gui.config.design_system import Colors, Typography, Spacing, BorderRadius, create_text, create_card, create_button, create_input
from gui.components.modern_table import create_modern_table

def get_detailed_data(search_term="", data_type="all"):
    """Get detailed data with search and filtering capabilities."""
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            data = {}
            search_filter = f"%{search_term}%" if search_term else "%"

            # Get teachers with comprehensive information
            if data_type in ["all", "teachers"]:
                cursor.execute("""
                    SELECT t.*, u.username, u.email, u.created_at,
                           COUNT(DISTINCT c.id) as class_count,
                           COUNT(DISTINCT m.id) as subject_count,
                           COUNT(DISTINCT q.id) as quiz_count,
                           COUNT(DISTINCT e.id) as student_count
                    FROM teachers t
                    LEFT JOIN users u ON t.user_id = u.id
                    LEFT JOIN classes c ON c.teacher_id = t.id
                    LEFT JOIN matieres m ON m.teacher_id = t.id
                    LEFT JOIN quiz q ON q.teacher_id = t.id
                    LEFT JOIN etudiants e ON e.class_id = c.id
                    WHERE t.full_name LIKE ? OR u.email LIKE ?
                    GROUP BY t.id
                    ORDER BY t.full_name
                """, (search_filter, search_filter))
                data['teachers'] = [dict(row) for row in cursor.fetchall()]

            # Get classes with detailed information
            if data_type in ["all", "classes"]:
                cursor.execute("""
                    SELECT c.*, t.full_name as teacher_name, t.email as teacher_email,
                           COUNT(DISTINCT e.id) as student_count,
                           COUNT(DISTINCT m.id) as subject_count,
                           COUNT(DISTINCT q.id) as quiz_count,
                           COUNT(DISTINCT p.id) as attendance_count
                    FROM classes c
                    LEFT JOIN teachers t ON c.teacher_id = t.id
                    LEFT JOIN etudiants e ON e.class_id = c.id
                    LEFT JOIN matieres m ON m.class_id = c.id
                    LEFT JOIN quiz q ON q.class_id = c.id
                    LEFT JOIN presences p ON p.student_id = e.id
                    WHERE c.name LIKE ? OR t.full_name LIKE ?
                    GROUP BY c.id
                    ORDER BY c.name
                """, (search_filter, search_filter))
                data['classes'] = [dict(row) for row in cursor.fetchall()]

            # Get students with detailed information
            if data_type in ["all", "students"]:
                cursor.execute("""
                    SELECT e.*, c.name as class_name, t.full_name as teacher_name,
                           COUNT(DISTINCT p.id) as attendance_count,
                           COUNT(DISTINCT sq.id) as quiz_submissions,
                           AVG(CASE WHEN p.status = 'present' THEN 1.0 ELSE 0.0 END) * 100 as attendance_rate
                    FROM etudiants e
                    LEFT JOIN classes c ON e.class_id = c.id
                    LEFT JOIN teachers t ON c.teacher_id = t.id
                    LEFT JOIN presences p ON p.student_id = e.id
                    LEFT JOIN soumissions_quiz sq ON sq.student_id = e.id
                    WHERE e.name LIKE ? OR c.name LIKE ?
                    GROUP BY e.id
                    ORDER BY e.name
                """, (search_filter, search_filter))
                data['students'] = [dict(row) for row in cursor.fetchall()]

            # Get subjects with detailed information
            if data_type in ["all", "subjects"]:
                cursor.execute("""
                    SELECT m.*, c.name as class_name, t.full_name as teacher_name,
                           COUNT(DISTINCT q.id) as quiz_count
                    FROM matieres m
                    LEFT JOIN classes c ON m.class_id = c.id
                    LEFT JOIN teachers t ON m.teacher_id = t.id
                    LEFT JOIN quiz q ON q.subject_id = m.id
                    WHERE m.name LIKE ? OR c.name LIKE ?
                    GROUP BY m.id
                    ORDER BY m.name
                """, (search_filter, search_filter))
                data['subjects'] = [dict(row) for row in cursor.fetchall()]

            # Get quizzes with detailed information
            if data_type in ["all", "quizzes"]:
                cursor.execute("""
                    SELECT q.*, c.name as class_name, m.name as subject_name, t.full_name as teacher_name,
                           COUNT(DISTINCT qq.id) as question_count,
                           COUNT(DISTINCT sq.id) as submission_count,
                           AVG(sq.score) as avg_score
                    FROM quiz q
                    LEFT JOIN classes c ON q.class_id = c.id
                    LEFT JOIN matieres m ON q.subject_id = m.id
                    LEFT JOIN teachers t ON q.teacher_id = t.id
                    LEFT JOIN questions_quiz qq ON qq.quiz_id = q.id
                    LEFT JOIN soumissions_quiz sq ON sq.quiz_id = q.id
                    WHERE q.title LIKE ? OR c.name LIKE ? OR m.name LIKE ?
                    GROUP BY q.id
                    ORDER BY q.created_at DESC
                """, (search_filter, search_filter, search_filter))
                data['quizzes'] = [dict(row) for row in cursor.fetchall()]

            # Get attendance with detailed information
            if data_type in ["all", "attendance"]:
                cursor.execute("""
                    SELECT p.*, e.name as student_name, c.name as class_name,
                           m.name as subject_name, t.full_name as teacher_name
                    FROM presences p
                    JOIN etudiants e ON p.student_id = e.id
                    JOIN classes c ON e.class_id = c.id
                    LEFT JOIN matieres m ON p.subject_id = m.id
                    LEFT JOIN teachers t ON c.teacher_id = t.id
                    WHERE e.name LIKE ? OR c.name LIKE ? OR m.name LIKE ?
                    ORDER BY p.date DESC, p.time DESC
                    LIMIT 100
                """, (search_filter, search_filter, search_filter))
                data['attendance'] = [dict(row) for row in cursor.fetchall()]

            return data

    except Exception as e:
        print(f"❌ Failed to get detailed data: {e}")
        return {}

def create_admin_data_view(page: ft.Page):
    """Create comprehensive admin data view with detailed information."""
    is_mobile = getattr(page, 'is_mobile', False)

    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/data", controls=[])

    # State variables
    search_term = ""
    selected_data_type = "all"

    # State variables
    search_term = ""
    selected_data_type = "all"

    # Get initial data
    data = get_detailed_data()

    # Header with search and filters
    def on_search_change(e):
        nonlocal search_term
        search_term = e.control.value
        # Apply search to existing tables if they exist
        if table_instances:
            apply_search_to_tables()
        else:
            refresh_data()

    def on_filter_change(e):
        nonlocal selected_data_type
        selected_data_type = e.control.value
        refresh_data()

    def refresh_data():
        nonlocal data
        data = get_detailed_data("", selected_data_type)  # Get fresh data without search filter
        update_content()
        # Apply search after updating content
        if search_term:
            apply_search_to_tables()

    header = ft.Container(
        content=ft.Column([
            create_text("System Data Overview", variant="h3", weight="semibold"),
            create_text("Comprehensive view of all system data with search and filtering", variant="body", color=Colors.TEXT_SECONDARY),
            ft.Container(height=Spacing.MD),
            ft.Row([
                ft.Container(
                    content=create_input("Search data...", hint="Enter search term", on_change=on_search_change),
                    expand=True
                ),
                ft.Container(
                    content=ft.Dropdown(
                        label="Data Type",
                        value="all",
                        options=[
                            ft.dropdown.Option("all", "All Data"),
                            ft.dropdown.Option("teachers", "Teachers"),
                            ft.dropdown.Option("classes", "Classes"),
                            ft.dropdown.Option("students", "Students"),
                            ft.dropdown.Option("subjects", "Subjects"),
                            ft.dropdown.Option("quizzes", "Quizzes"),
                            ft.dropdown.Option("attendance", "Attendance"),
                        ],
                        on_change=on_filter_change,
                        border_color=Colors.GREY_300,
                        focused_border_color=Colors.SECONDARY,
                    ),
                    width=200
                )
            ], spacing=Spacing.MD)
        ], spacing=Spacing.SM),
        padding=ft.padding.only(bottom=Spacing.LG)
    )

    # Data display container
    data_container = ft.Column(spacing=Spacing.LG)

    # Store table instances for search functionality
    table_instances = {}

    def update_content():
        """Update the data container with current data using modern tables."""
        data_container.controls.clear()
        table_instances.clear()

        if selected_data_type == "all" or selected_data_type == "teachers":
            if data.get('teachers'):
                table_container, table_instance = create_modern_table(
                    page, data['teachers'],
                    ['full_name', 'email', 'class_count', 'subject_count', 'quiz_count', 'student_count'],
                    "Teachers", ft.Icons.PERSON
                )
                data_container.controls.append(table_container)
                table_instances['teachers'] = table_instance

        if selected_data_type == "all" or selected_data_type == "classes":
            if data.get('classes'):
                table_container, table_instance = create_modern_table(
                    page, data['classes'],
                    ['name', 'teacher_name', 'student_count', 'subject_count', 'quiz_count'],
                    "Classes", ft.Icons.SCHOOL
                )
                data_container.controls.append(table_container)
                table_instances['classes'] = table_instance

        if selected_data_type == "all" or selected_data_type == "students":
            if data.get('students'):
                table_container, table_instance = create_modern_table(
                    page, data['students'],
                    ['name', 'class_name', 'teacher_name', 'attendance_count', 'attendance_rate', 'quiz_submissions'],
                    "Students", ft.Icons.PEOPLE
                )
                data_container.controls.append(table_container)
                table_instances['students'] = table_instance

        if selected_data_type == "all" or selected_data_type == "subjects":
            if data.get('subjects'):
                table_container, table_instance = create_modern_table(
                    page, data['subjects'],
                    ['name', 'class_name', 'teacher_name', 'quiz_count'],
                    "Subjects", ft.Icons.BOOK
                )
                data_container.controls.append(table_container)
                table_instances['subjects'] = table_instance

        if selected_data_type == "all" or selected_data_type == "quizzes":
            if data.get('quizzes'):
                table_container, table_instance = create_modern_table(
                    page, data['quizzes'],
                    ['title', 'class_name', 'subject_name', 'question_count', 'submission_count', 'avg_score'],
                    "Quizzes", ft.Icons.QUIZ
                )
                data_container.controls.append(table_container)
                table_instances['quizzes'] = table_instance

        if selected_data_type == "all" or selected_data_type == "attendance":
            if data.get('attendance'):
                table_container, table_instance = create_modern_table(
                    page, data['attendance'],
                    ['student_name', 'class_name', 'subject_name', 'status', 'date', 'time'],
                    "Attendance Records", ft.Icons.CHECK_CIRCLE
                )
                data_container.controls.append(table_container)
                table_instances['attendance'] = table_instance

        page.update()

    def apply_search_to_tables():
        """Apply search term to all visible tables."""
        for table_instance in table_instances.values():
            table_instance.filter_data(search_term)

    # Initialize content
    update_content()

    # Main content
    content = [header, data_container]

    return create_admin_page_layout(
        page,
        "System Data",
        content
    )
