"""
Modern admin dashboard with comprehensive data overview.
"""
import flet as ft
import sqlite3
from gui.components.admin_layout import create_admin_page_layout
from gui.services.auth_service import AuthService
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_TEACHERS
from gui.config.language import get_text
from gui.config.design_system import Colors, Typography, Spacing, BorderRadius, create_text, create_card, create_button
from facial_recognition_system.config import Config

def get_comprehensive_data():
    """Get comprehensive system data for admin dashboard."""
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            data = {}

            # Get system statistics
            cursor.execute("SELECT COUNT(*) as count FROM teachers")
            data['total_teachers'] = cursor.fetchone()['count']

            cursor.execute("SELECT COUNT(*) as count FROM classes")
            data['total_classes'] = cursor.fetchone()['count']

            cursor.execute("SELECT COUNT(*) as count FROM etudiants")
            data['total_students'] = cursor.fetchone()['count']

            cursor.execute("SELECT COUNT(*) as count FROM matieres")
            data['total_subjects'] = cursor.fetchone()['count']

            cursor.execute("SELECT COUNT(*) as count FROM quiz")
            data['total_quizzes'] = cursor.fetchone()['count']

            cursor.execute("""
                SELECT COUNT(*) as count FROM presences
                WHERE date(date) = date('now')
            """)
            data['today_attendance'] = cursor.fetchone()['count']

            # Get recent activity (last 7 days)
            cursor.execute("""
                SELECT e.name as student_name,
                       c.name as class_name,
                       p.status, p.date, p.time
                FROM presences p
                JOIN etudiants e ON p.student_id = e.id
                JOIN classes c ON e.class_id = c.id
                WHERE date(p.date) >= date('now', '-7 days')
                ORDER BY p.date DESC, p.time DESC
                LIMIT 10
            """)
            data['recent_activity'] = [dict(row) for row in cursor.fetchall()]

            return data

    except Exception as e:
        print(f"❌ Failed to get comprehensive data: {e}")
        return {}

def create_admin_dashboard_view(page: ft.Page):
    """Create comprehensive admin dashboard view."""
    is_mobile = getattr(page, 'is_mobile', False)

    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin", controls=[])

    # Get comprehensive data
    data = get_comprehensive_data()

    # Header section
    header = ft.Container(
        content=ft.Column([
            create_text(f"Welcome, {current_user.get('full_name', 'Admin')}", variant="h3", weight="semibold"),
            create_text("System Overview & Management", variant="body", color=Colors.TEXT_SECONDARY)
        ], spacing=Spacing.XS),
        padding=ft.padding.only(bottom=Spacing.LG)
    )

    # Statistics cards
    def create_stat_card(title: str, value: int, subtitle: str = None, color: str = Colors.SECONDARY):
        return create_card(
            ft.Column([
                ft.Row([
                    ft.Container(
                        content=ft.Icon(ft.Icons.ANALYTICS, size=20, color=color),
                        width=40,
                        height=40,
                        bgcolor=Colors.SURFACE,
                        border_radius=BorderRadius.SM,
                        alignment=ft.alignment.center
                    ),
                    ft.Column([
                        create_text(str(value), variant="h4", weight="bold"),
                        create_text(title, variant="body_small", color=Colors.TEXT_SECONDARY)
                    ], spacing=0, expand=True)
                ], spacing=Spacing.SM),
                create_text(subtitle or "", variant="caption", color=Colors.TEXT_SECONDARY) if subtitle else ft.Container()
            ], spacing=Spacing.XS),
            padding=Spacing.MD
        )

    # Statistics grid
    stats_grid = ft.ResponsiveRow([
        ft.Container(
            create_stat_card("Teachers", data.get('total_teachers', 0), "Active educators"),
            col={"sm": 6, "md": 4, "lg": 2}
        ),
        ft.Container(
            create_stat_card("Classes", data.get('total_classes', 0), "Learning groups"),
            col={"sm": 6, "md": 4, "lg": 2}
        ),
        ft.Container(
            create_stat_card("Students", data.get('total_students', 0), "Enrolled learners"),
            col={"sm": 6, "md": 4, "lg": 2}
        ),
        ft.Container(
            create_stat_card("Subjects", data.get('total_subjects', 0), "Course topics"),
            col={"sm": 6, "md": 4, "lg": 2}
        ),
        ft.Container(
            create_stat_card("Quizzes", data.get('total_quizzes', 0), "Assessments"),
            col={"sm": 6, "md": 4, "lg": 2}
        ),
        ft.Container(
            create_stat_card("Today", data.get('today_attendance', 0), "Attendance records"),
            col={"sm": 6, "md": 4, "lg": 2}
        ),
    ], spacing=Spacing.SM)

    # Recent activity section
    recent_activity = create_card(
        ft.Column([
            ft.Row([
                create_text("Recent Activity", variant="h6", weight="semibold"),
                ft.Container(expand=True),
                create_button("View All", variant="outline", size="small")
            ]),
            ft.Container(height=Spacing.SM),
            ft.Column([
                ft.Row([
                    ft.Container(
                        content=ft.Icon(ft.Icons.CIRCLE, size=8, color=Colors.SUCCESS if activity.get('status') == 'present' else Colors.ERROR),
                        width=20,
                        alignment=ft.alignment.center
                    ),
                    ft.Column([
                        create_text(f"{activity.get('student_name', 'Unknown')}", variant="body_small", weight="medium"),
                        create_text(f"{activity.get('class_name', '')} • {activity.get('date', '')} {activity.get('time', '')}", variant="caption", color=Colors.TEXT_SECONDARY)
                    ], spacing=0, expand=True),
                    create_text(activity.get('status', '').title(), variant="caption",
                              color=Colors.SUCCESS if activity.get('status') == 'present' else Colors.ERROR)
                ], spacing=Spacing.SM)
                for activity in data.get('recent_activity', [])[:5]
            ], spacing=Spacing.SM) if data.get('recent_activity') else create_text("No recent activity", variant="body", color=Colors.TEXT_SECONDARY)
        ], spacing=Spacing.SM),
        padding=Spacing.LG
    )

    # Quick actions section
    quick_actions = create_card(
        ft.Column([
            create_text("Quick Actions", variant="h6", weight="semibold"),
            ft.Container(height=Spacing.SM),
            ft.Row([
                create_button("Manage Teachers", on_click=lambda _: page.go(ROUTE_ADMIN_TEACHERS)),
                create_button("View Data", variant="outline", on_click=lambda _: page.go("/admin/data")),
            ], spacing=Spacing.SM, wrap=True)
        ], spacing=Spacing.SM),
        padding=Spacing.LG
    )

    # Main content layout
    content = [
        header,
        stats_grid,
        ft.ResponsiveRow([
            ft.Container(recent_activity, col={"sm": 12, "md": 8}),
            ft.Container(quick_actions, col={"sm": 12, "md": 4})
        ], spacing=Spacing.SM)
    ]

    return create_admin_page_layout(
        page,
        "Admin Dashboard",
        content
    )

