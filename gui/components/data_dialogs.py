"""
Data dialog components for displaying system data in popup windows.
"""
import flet as ft
import sqlite3
from facial_recognition_system.config import Config
from gui.config.design_system import Colors, Typography, Spacing, BorderRadius, create_text, create_card, create_button

def get_teachers_data():
    """Get comprehensive teachers data."""
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT t.*, u.username, u.email,
                       COUNT(DISTINCT c.id) as class_count,
                       COUNT(DISTINCT m.id) as subject_count,
                       COUNT(DISTINCT q.id) as quiz_count,
                       COUNT(DISTINCT e.id) as student_count
                FROM teachers t
                LEFT JOIN users u ON t.user_id = u.id
                LEFT JOIN classes c ON c.teacher_id = t.id
                LEFT JOIN matieres m ON m.teacher_id = t.id
                LEFT JOIN quiz q ON q.teacher_id = t.id
                LEFT JOIN etudiants e ON e.class_id = c.id
                GROUP BY t.id
                ORDER BY t.full_name
            """)
            return [dict(row) for row in cursor.fetchall()]
    except Exception as e:
        print(f"❌ Failed to get teachers data: {e}")
        return []

def get_classes_data():
    """Get comprehensive classes data."""
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT c.*, t.full_name as teacher_name,
                       COUNT(DISTINCT e.id) as student_count,
                       COUNT(DISTINCT m.id) as subject_count,
                       COUNT(DISTINCT q.id) as quiz_count
                FROM classes c
                LEFT JOIN teachers t ON c.teacher_id = t.id
                LEFT JOIN etudiants e ON e.class_id = c.id
                LEFT JOIN matieres m ON m.class_id = c.id
                LEFT JOIN quiz q ON q.class_id = c.id
                GROUP BY c.id
                ORDER BY c.name
            """)
            return [dict(row) for row in cursor.fetchall()]
    except Exception as e:
        print(f"❌ Failed to get classes data: {e}")
        return []

def get_students_data():
    """Get comprehensive students data."""
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT e.*, c.name as class_name, t.full_name as teacher_name,
                       COUNT(DISTINCT p.id) as attendance_count,
                       COUNT(DISTINCT sq.id) as quiz_submissions,
                       AVG(CASE WHEN p.status = 'present' THEN 1.0 ELSE 0.0 END) * 100 as attendance_rate
                FROM etudiants e
                LEFT JOIN classes c ON e.class_id = c.id
                LEFT JOIN teachers t ON c.teacher_id = t.id
                LEFT JOIN presences p ON p.student_id = e.id
                LEFT JOIN soumissions_quiz sq ON sq.student_id = e.id
                GROUP BY e.id
                ORDER BY e.name
            """)
            return [dict(row) for row in cursor.fetchall()]
    except Exception as e:
        print(f"❌ Failed to get students data: {e}")
        return []

def get_subjects_data():
    """Get comprehensive subjects data."""
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT m.*, c.name as class_name, t.full_name as teacher_name,
                       COUNT(DISTINCT q.id) as quiz_count
                FROM matieres m
                LEFT JOIN classes c ON m.class_id = c.id
                LEFT JOIN teachers t ON m.teacher_id = t.id
                LEFT JOIN quiz q ON q.subject_id = m.id
                GROUP BY m.id
                ORDER BY m.name
            """)
            return [dict(row) for row in cursor.fetchall()]
    except Exception as e:
        print(f"❌ Failed to get subjects data: {e}")
        return []

def get_quizzes_data():
    """Get comprehensive quizzes data."""
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT q.*, c.name as class_name, m.name as subject_name, t.full_name as teacher_name,
                       COUNT(DISTINCT qq.id) as question_count,
                       COUNT(DISTINCT sq.id) as submission_count,
                       AVG(sq.score) as avg_score
                FROM quiz q
                LEFT JOIN classes c ON q.class_id = c.id
                LEFT JOIN matieres m ON q.subject_id = m.id
                LEFT JOIN teachers t ON q.teacher_id = t.id
                LEFT JOIN questions_quiz qq ON qq.quiz_id = q.id
                LEFT JOIN soumissions_quiz sq ON sq.quiz_id = q.id
                GROUP BY q.id
                ORDER BY q.created_at DESC
            """)
            return [dict(row) for row in cursor.fetchall()]
    except Exception as e:
        print(f"❌ Failed to get quizzes data: {e}")
        return []

def get_attendance_data():
    """Get today's attendance data."""
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT p.*, e.name as student_name, c.name as class_name, 
                       m.name as subject_name, t.full_name as teacher_name
                FROM presences p
                JOIN etudiants e ON p.student_id = e.id
                JOIN classes c ON e.class_id = c.id
                LEFT JOIN matieres m ON p.subject_id = m.id
                LEFT JOIN teachers t ON c.teacher_id = t.id
                WHERE date(p.date) = date('now')
                ORDER BY p.time DESC
            """)
            return [dict(row) for row in cursor.fetchall()]
    except Exception as e:
        print(f"❌ Failed to get attendance data: {e}")
        return []

def create_data_table(data: list, columns: list):
    """Create a simple data table for dialog display."""
    if not data:
        return ft.Container(
            content=create_text("No data available", variant="body", color=Colors.TEXT_SECONDARY),
            alignment=ft.alignment.center,
            padding=ft.padding.all(Spacing.XL)
        )
    
    # Create table rows
    rows = []
    for item in data[:50]:  # Limit to 50 items for dialog
        cells = []
        for col in columns:
            value = item.get(col, '')
            if value is None:
                value = ""
            elif isinstance(value, float):
                if col.endswith('_rate'):
                    value = f"{value:.1f}%"
                elif col.endswith('_score'):
                    value = f"{value:.1f}"
                else:
                    value = str(int(value))
            else:
                value = str(value)
            
            if len(value) > 25:
                value = value[:22] + "..."
            
            cells.append(ft.DataCell(
                create_text(value, variant="body_small")
            ))
        rows.append(ft.DataRow(cells=cells))
    
    # Create table columns
    table_columns = []
    for col in columns:
        header_text = col.replace('_', ' ').title()
        if col.endswith('_count'):
            header_text = header_text.replace(' Count', '')
        elif col.endswith('_rate'):
            header_text = header_text.replace(' Rate', ' %')
        table_columns.append(ft.DataColumn(
            create_text(header_text, variant="body_small", weight="semibold")
        ))
    
    return ft.Container(
        content=ft.DataTable(
            columns=table_columns,
            rows=rows,
            border=ft.border.all(1, Colors.GREY_200),
            border_radius=BorderRadius.SM,
            heading_row_color=Colors.SURFACE,
            show_checkbox_column=False,
            column_spacing=15,
        ),
        scroll=ft.ScrollMode.AUTO,
        height=400,
        width=800
    )

def show_teachers_dialog(page: ft.Page):
    """Show teachers data in a dialog."""
    data = get_teachers_data()
    columns = ['full_name', 'email', 'class_count', 'subject_count', 'quiz_count', 'student_count']
    
    dialog = ft.AlertDialog(
        title=ft.Row([
            ft.Icon(ft.Icons.PERSON, color=Colors.SECONDARY),
            create_text("Teachers", variant="h6", weight="semibold")
        ], spacing=Spacing.SM),
        content=create_data_table(data, columns),
        actions=[
            ft.TextButton("Close", on_click=lambda _: close_dialog(page))
        ],
        actions_alignment=ft.MainAxisAlignment.END
    )
    
    page.dialog = dialog
    dialog.open = True
    page.update()

def show_classes_dialog(page: ft.Page):
    """Show classes data in a dialog."""
    data = get_classes_data()
    columns = ['name', 'teacher_name', 'student_count', 'subject_count', 'quiz_count']
    
    dialog = ft.AlertDialog(
        title=ft.Row([
            ft.Icon(ft.Icons.SCHOOL, color=Colors.SECONDARY),
            create_text("Classes", variant="h6", weight="semibold")
        ], spacing=Spacing.SM),
        content=create_data_table(data, columns),
        actions=[
            ft.TextButton("Close", on_click=lambda _: close_dialog(page))
        ],
        actions_alignment=ft.MainAxisAlignment.END
    )
    
    page.dialog = dialog
    dialog.open = True
    page.update()

def show_students_dialog(page: ft.Page):
    """Show students data in a dialog."""
    data = get_students_data()
    columns = ['name', 'class_name', 'teacher_name', 'attendance_count', 'attendance_rate', 'quiz_submissions']
    
    dialog = ft.AlertDialog(
        title=ft.Row([
            ft.Icon(ft.Icons.PEOPLE, color=Colors.SECONDARY),
            create_text("Students", variant="h6", weight="semibold")
        ], spacing=Spacing.SM),
        content=create_data_table(data, columns),
        actions=[
            ft.TextButton("Close", on_click=lambda _: close_dialog(page))
        ],
        actions_alignment=ft.MainAxisAlignment.END
    )
    
    page.dialog = dialog
    dialog.open = True
    page.update()

def show_subjects_dialog(page: ft.Page):
    """Show subjects data in a dialog."""
    data = get_subjects_data()
    columns = ['name', 'class_name', 'teacher_name', 'quiz_count']
    
    dialog = ft.AlertDialog(
        title=ft.Row([
            ft.Icon(ft.Icons.BOOK, color=Colors.SECONDARY),
            create_text("Subjects", variant="h6", weight="semibold")
        ], spacing=Spacing.SM),
        content=create_data_table(data, columns),
        actions=[
            ft.TextButton("Close", on_click=lambda _: close_dialog(page))
        ],
        actions_alignment=ft.MainAxisAlignment.END
    )
    
    page.dialog = dialog
    dialog.open = True
    page.update()

def show_quizzes_dialog(page: ft.Page):
    """Show quizzes data in a dialog."""
    data = get_quizzes_data()
    columns = ['title', 'class_name', 'subject_name', 'question_count', 'submission_count', 'avg_score']
    
    dialog = ft.AlertDialog(
        title=ft.Row([
            ft.Icon(ft.Icons.QUIZ, color=Colors.SECONDARY),
            create_text("Quizzes", variant="h6", weight="semibold")
        ], spacing=Spacing.SM),
        content=create_data_table(data, columns),
        actions=[
            ft.TextButton("Close", on_click=lambda _: close_dialog(page))
        ],
        actions_alignment=ft.MainAxisAlignment.END
    )
    
    page.dialog = dialog
    dialog.open = True
    page.update()

def show_attendance_dialog(page: ft.Page):
    """Show today's attendance data in a dialog."""
    data = get_attendance_data()
    columns = ['student_name', 'class_name', 'subject_name', 'status', 'time']
    
    dialog = ft.AlertDialog(
        title=ft.Row([
            ft.Icon(ft.Icons.CHECK_CIRCLE, color=Colors.SECONDARY),
            create_text("Today's Attendance", variant="h6", weight="semibold")
        ], spacing=Spacing.SM),
        content=create_data_table(data, columns),
        actions=[
            ft.TextButton("Close", on_click=lambda _: close_dialog(page))
        ],
        actions_alignment=ft.MainAxisAlignment.END
    )
    
    page.dialog = dialog
    dialog.open = True
    page.update()

def close_dialog(page: ft.Page):
    """Close the current dialog."""
    if page.dialog:
        page.dialog.open = False
        page.update()
