"""
Modern data table component with pagination, sorting, and responsive design.
"""
import flet as ft
import math
from gui.config.design_system import Colors, Typography, Spacing, BorderRadius, create_text, create_card, create_button

class ModernDataTable:
    """Modern data table with advanced features."""
    
    def __init__(self, page: ft.Page, data: list, columns: list, title: str = "", icon: str = ft.Icons.TABLE_CHART):
        self.page = page
        self.original_data = data
        self.columns = columns
        self.title = title
        self.icon = icon
        
        # Table state
        self.current_page = 0
        self.items_per_page = 20
        self.sort_column = None
        self.sort_ascending = True
        self.filtered_data = data.copy()
        
        # UI components
        self.table_container = ft.Column(spacing=Spacing.SM)
        self.pagination_container = ft.Row(alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
        self.info_container = ft.Row(alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
        
        self._build_table()
    
    def _build_table(self):
        """Build the complete table with header, data, and pagination."""
        self.table_container.controls.clear()
        
        # Header with title and info
        header = ft.Row([
            ft.Icon(self.icon, size=24, color=Colors.SECONDARY),
            create_text(self.title, variant="h6", weight="semibold"),
            ft.Container(expand=True),
            create_text(f"{len(self.filtered_data)} items", variant="caption", color=Colors.TEXT_SECONDARY)
        ], spacing=Spacing.SM)
        
        # Data table
        data_table = self._create_data_table()
        
        # Pagination
        pagination = self._create_pagination()
        
        self.table_container.controls.extend([header, data_table, pagination])
    
    def _create_data_table(self):
        """Create the data table with current page data."""
        if not self.filtered_data:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.INBOX, size=48, color=Colors.TEXT_DISABLED),
                    create_text("No data available", variant="body", color=Colors.TEXT_SECONDARY)
                ], spacing=Spacing.SM, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                alignment=ft.alignment.center,
                padding=ft.padding.all(Spacing.XL)
            )
        
        # Calculate pagination
        start_idx = self.current_page * self.items_per_page
        end_idx = min(start_idx + self.items_per_page, len(self.filtered_data))
        page_data = self.filtered_data[start_idx:end_idx]
        
        # Create table columns with sorting
        table_columns = []
        for i, col in enumerate(self.columns):
            header_text = col.replace('_', ' ').title()
            if col.endswith('_count'):
                header_text = header_text.replace(' Count', '')
            elif col.endswith('_rate'):
                header_text = header_text.replace(' Rate', ' %')
            
            # Add sort indicator
            sort_icon = None
            if self.sort_column == col:
                sort_icon = ft.Icons.ARROW_UPWARD if self.sort_ascending else ft.Icons.ARROW_DOWNWARD
            
            column_content = ft.Row([
                create_text(header_text, variant="body_small", weight="semibold"),
                ft.Icon(sort_icon, size=16, color=Colors.TEXT_SECONDARY) if sort_icon else ft.Container()
            ], spacing=Spacing.XS)
            
            table_columns.append(ft.DataColumn(
                column_content,
                on_sort=lambda e, column=col: self._sort_data(column)
            ))
        
        # Create table rows
        rows = []
        for item in page_data:
            cells = []
            for col in self.columns:
                value = item.get(col, '')
                if value is None:
                    value = ""
                elif isinstance(value, float):
                    if col.endswith('_rate'):
                        value = f"{value:.1f}%"
                    elif col.endswith('_score'):
                        value = f"{value:.1f}"
                    else:
                        value = str(int(value))
                else:
                    value = str(value)
                
                if len(value) > 30:
                    value = value[:27] + "..."
                
                cells.append(ft.DataCell(
                    create_text(value, variant="body_small")
                ))
            rows.append(ft.DataRow(cells=cells))
        
        return ft.Container(
            content=ft.DataTable(
                columns=table_columns,
                rows=rows,
                border=ft.border.all(1, Colors.GREY_200),
                border_radius=BorderRadius.SM,
                heading_row_color=Colors.SURFACE,
                show_checkbox_column=False,
                column_spacing=20,
                data_row_min_height=40,
                data_row_max_height=60,
            ),
            scroll=ft.ScrollMode.AUTO
        )
    
    def _create_pagination(self):
        """Create pagination controls."""
        total_pages = math.ceil(len(self.filtered_data) / self.items_per_page)
        
        if total_pages <= 1:
            return ft.Container()
        
        # Page info
        start_item = self.current_page * self.items_per_page + 1
        end_item = min((self.current_page + 1) * self.items_per_page, len(self.filtered_data))
        
        page_info = create_text(
            f"Showing {start_item}-{end_item} of {len(self.filtered_data)} items",
            variant="caption",
            color=Colors.TEXT_SECONDARY
        )
        
        # Navigation buttons
        prev_button = ft.IconButton(
            icon=ft.Icons.CHEVRON_LEFT,
            on_click=self._prev_page,
            disabled=self.current_page == 0,
            icon_color=Colors.TEXT_SECONDARY,
            icon_size=20
        )
        
        next_button = ft.IconButton(
            icon=ft.Icons.CHEVRON_RIGHT,
            on_click=self._next_page,
            disabled=self.current_page >= total_pages - 1,
            icon_color=Colors.TEXT_SECONDARY,
            icon_size=20
        )
        
        # Page numbers (show current and nearby pages)
        page_buttons = []
        start_page = max(0, self.current_page - 2)
        end_page = min(total_pages, self.current_page + 3)
        
        for i in range(start_page, end_page):
            is_current = i == self.current_page
            page_buttons.append(
                ft.TextButton(
                    text=str(i + 1),
                    style=ft.ButtonStyle(
                        bgcolor=Colors.SECONDARY if is_current else Colors.SURFACE,
                        color=Colors.TEXT_ON_PRIMARY if is_current else Colors.TEXT_SECONDARY,
                        text_style=ft.TextStyle(weight=Typography.MEDIUM if is_current else Typography.REGULAR),
                        shape=ft.RoundedRectangleBorder(radius=BorderRadius.SM),
                        padding=ft.padding.symmetric(horizontal=Spacing.SM, vertical=Spacing.XS)
                    ),
                    on_click=lambda e, page=i: self._go_to_page(page)
                )
            )
        
        navigation = ft.Row([
            prev_button,
            *page_buttons,
            next_button
        ], spacing=Spacing.XS)
        
        return ft.Row([
            page_info,
            navigation
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
    
    def _sort_data(self, column: str):
        """Sort data by column."""
        if self.sort_column == column:
            self.sort_ascending = not self.sort_ascending
        else:
            self.sort_column = column
            self.sort_ascending = True
        
        def sort_key(item):
            value = item.get(column, '')
            if isinstance(value, (int, float)):
                return value
            return str(value).lower()
        
        self.filtered_data.sort(key=sort_key, reverse=not self.sort_ascending)
        self.current_page = 0  # Reset to first page after sorting
        self._build_table()
        self.page.update()
    
    def _prev_page(self, e):
        """Go to previous page."""
        if self.current_page > 0:
            self.current_page -= 1
            self._build_table()
            self.page.update()
    
    def _next_page(self, e):
        """Go to next page."""
        total_pages = math.ceil(len(self.filtered_data) / self.items_per_page)
        if self.current_page < total_pages - 1:
            self.current_page += 1
            self._build_table()
            self.page.update()
    
    def _go_to_page(self, page: int):
        """Go to specific page."""
        self.current_page = page
        self._build_table()
        self.page.update()
    
    def filter_data(self, search_term: str = ""):
        """Filter data based on search term."""
        if not search_term:
            self.filtered_data = self.original_data.copy()
        else:
            search_lower = search_term.lower()
            self.filtered_data = [
                item for item in self.original_data
                if any(search_lower in str(item.get(col, '')).lower() for col in self.columns)
            ]
        
        self.current_page = 0  # Reset to first page after filtering
        self._build_table()
        self.page.update()
    
    def update_data(self, new_data: list):
        """Update table data."""
        self.original_data = new_data
        self.filtered_data = new_data.copy()
        self.current_page = 0
        self._build_table()
        self.page.update()
    
    def get_container(self):
        """Get the table container."""
        return create_card(self.table_container, padding=Spacing.LG)

def create_modern_table(page: ft.Page, data: list, columns: list, title: str = "", icon: str = ft.Icons.TABLE_CHART):
    """Create a modern data table with pagination and sorting."""
    table = ModernDataTable(page, data, columns, title, icon)
    return table.get_container(), table
