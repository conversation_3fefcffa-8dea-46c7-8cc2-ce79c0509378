"""
Modern admin dashboard with comprehensive data overview.
"""
import flet as ft
import sqlite3
from gui.components.admin_layout import create_admin_page_layout
from gui.services.auth_service import AuthService
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_TEACHERS
from gui.config.language import get_text
from gui.config.design_system import Colors, Typography, Spacing, BorderRadius, create_text, create_card, create_button
from facial_recognition_system.config import Config
from gui.components.data_dialogs import (
    show_teachers_dialog, show_classes_dialog, show_students_dialog,
    show_subjects_dialog, show_quizzes_dialog, show_attendance_dialog
)

def get_comprehensive_data():
    """Get comprehensive system data for admin dashboard."""
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            data = {}

            # Get system statistics
            cursor.execute("SELECT COUNT(*) as count FROM teachers")
            data['total_teachers'] = cursor.fetchone()['count']

            cursor.execute("SELECT COUNT(*) as count FROM classes")
            data['total_classes'] = cursor.fetchone()['count']

            cursor.execute("SELECT COUNT(*) as count FROM etudiants")
            data['total_students'] = cursor.fetchone()['count']

            cursor.execute("SELECT COUNT(*) as count FROM matieres")
            data['total_subjects'] = cursor.fetchone()['count']

            cursor.execute("SELECT COUNT(*) as count FROM quiz")
            data['total_quizzes'] = cursor.fetchone()['count']

            cursor.execute("""
                SELECT COUNT(*) as count FROM presences
                WHERE date(date) = date('now') AND status = 1
            """)
            data['today_attendance'] = cursor.fetchone()['count']



            return data

    except Exception as e:
        print(f"❌ Failed to get comprehensive data: {e}")
        return {}

def create_admin_dashboard_view(page: ft.Page):
    """Create comprehensive admin dashboard view for desktop."""
    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin", controls=[])

    # Get comprehensive data
    data = get_comprehensive_data()

    # Header section
    header = ft.Container(
        content=ft.Column([
            create_text(f"Welcome, {current_user.get('full_name', 'Admin')}", variant="h3", weight="semibold"),
            create_text("System Overview & Management", variant="body", color=Colors.TEXT_SECONDARY)
        ], spacing=Spacing.XS),
        padding=ft.padding.only(bottom=Spacing.LG)
    )

    # Statistics cards with dialog actions
    def create_stat_card(title: str, value: int, subtitle: str = None, color: str = Colors.SECONDARY, on_click=None):
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Container(
                        content=ft.Icon(ft.Icons.ANALYTICS, size=20, color=color),
                        width=40,
                        height=40,
                        bgcolor=Colors.SURFACE,
                        border_radius=BorderRadius.SM,
                        alignment=ft.alignment.center
                    ),
                    ft.Column([
                        create_text(str(value), variant="h4", weight="bold"),
                        create_text(title, variant="body_small", color=Colors.TEXT_SECONDARY)
                    ], spacing=0, expand=True)
                ], spacing=Spacing.SM),
                create_text(subtitle or "", variant="caption", color=Colors.TEXT_SECONDARY) if subtitle else ft.Container(),
                create_text("Click to view details", variant="caption", color=Colors.SECONDARY) if on_click else ft.Container()
            ], spacing=Spacing.XS),
            padding=ft.padding.all(Spacing.MD),
            margin=ft.margin.all(Spacing.SM),
            bgcolor=Colors.CARD,
            border_radius=ft.border_radius.all(BorderRadius.MD),
            border=ft.border.all(1, Colors.GREY_200),
            on_click=on_click,
            ink=True if on_click else False,
            width=180
        )

    # Statistics grid for desktop with dialog actions
    stats_grid = ft.Row([
        create_stat_card("Teachers", data.get('total_teachers', 0), "Active educators",
                        Colors.SECONDARY, lambda _: show_teachers_dialog(page)),
        create_stat_card("Classes", data.get('total_classes', 0), "Learning groups",
                        Colors.SECONDARY, lambda _: show_classes_dialog(page)),
        create_stat_card("Students", data.get('total_students', 0), "Enrolled learners",
                        Colors.SECONDARY, lambda _: show_students_dialog(page)),
        create_stat_card("Subjects", data.get('total_subjects', 0), "Course topics",
                        Colors.SECONDARY, lambda _: show_subjects_dialog(page)),
        create_stat_card("Quizzes", data.get('total_quizzes', 0), "Assessments",
                        Colors.SECONDARY, lambda _: show_quizzes_dialog(page)),
        create_stat_card("Today", data.get('today_attendance', 0), "Attendance records",
                        Colors.SECONDARY, lambda _: show_attendance_dialog(page)),
    ], spacing=Spacing.MD, wrap=True, alignment=ft.MainAxisAlignment.CENTER)

    # Main content layout
    content = [
        header,
        stats_grid
    ]

    return create_admin_page_layout(
        page,
        "Admin Dashboard",
        content
    )

