"""
Admin layout components with strict modern design.
"""
import flet as ft
from gui.config.constants import ROUTE_ADMIN_DASHBOARD, ROUTE_ADMIN_TEACHERS, ROUTE_LOGIN
from gui.config.language import get_text, DEFAULT_LANGUAGE
from gui.config.design_system import Colors, Typography, Spacing, BorderRadius, create_text, create_button

def create_admin_app_bar(page: ft.Page, title: str):
    """Create admin app bar with navigation."""
    current_user = getattr(page.app_state, 'current_user', None)
    current_language = getattr(page, 'language', DEFAULT_LANGUAGE)

    def logout_click(_):
        """Handle logout."""
        page.session.remove("current_user")
        page.app_state.current_user = None
        page.go(ROUTE_LOGIN)

    def dashboard_click(_):
        """Navigate to admin dashboard."""
        page.go(ROUTE_ADMIN_DASHBOARD)

    def teachers_click(_):
        """Navigate to teacher management."""
        page.go(ROUTE_ADMIN_TEACHERS)



    # Strict modern navigation with minimal design
    nav_buttons = ft.Row([
        ft.TextButton(
            "Dashboard",
            icon=ft.Icons.DASHBOARD,
            style=ft.ButtonStyle(
                color=Colors.TEXT_ON_PRIMARY if page.route == ROUTE_ADMIN_DASHBOARD else Colors.TEXT_SECONDARY,
                bgcolor=Colors.PRIMARY if page.route == ROUTE_ADMIN_DASHBOARD else Colors.SURFACE,
                text_style=ft.TextStyle(weight=Typography.MEDIUM, size=Typography.BODY),
                shape=ft.RoundedRectangleBorder(radius=BorderRadius.SM),
                padding=ft.padding.symmetric(horizontal=Spacing.MD, vertical=Spacing.SM)
            ),
            on_click=dashboard_click
        ),
        ft.TextButton(
            "Teachers",
            icon=ft.Icons.PEOPLE,
            style=ft.ButtonStyle(
                color=Colors.TEXT_ON_PRIMARY if page.route == ROUTE_ADMIN_TEACHERS else Colors.TEXT_SECONDARY,
                bgcolor=Colors.PRIMARY if page.route == ROUTE_ADMIN_TEACHERS else Colors.SURFACE,
                text_style=ft.TextStyle(weight=Typography.MEDIUM, size=Typography.BODY),
                shape=ft.RoundedRectangleBorder(radius=BorderRadius.SM),
                padding=ft.padding.symmetric(horizontal=Spacing.MD, vertical=Spacing.SM)
            ),
            on_click=teachers_click
        ),
    ], spacing=Spacing.SM)

    # Minimal user section
    user_section = ft.Row([
        create_text(
            f"Admin: {current_user.get('full_name', 'Admin') if current_user else 'Admin'}",
            variant="body_small",
            color=Colors.TEXT_SECONDARY
        ),
        ft.IconButton(
            icon=ft.Icons.LOGOUT,
            icon_color=Colors.ERROR,
            tooltip="Logout",
            on_click=logout_click,
            icon_size=18
        )
    ], spacing=Spacing.SM)

    return ft.AppBar(
        title=create_text(title, variant="h5", weight="semibold", color=Colors.TEXT_PRIMARY),
        center_title=False,
        bgcolor=Colors.WHITE,
        elevation=0,
        shadow_color=Colors.GREY_200,
        actions=[
            ft.Container(
                content=nav_buttons,
                padding=ft.padding.symmetric(horizontal=Spacing.SM),
                margin=ft.margin.only(right=Spacing.SM)
            ),
            ft.Container(
                content=user_section,
                padding=ft.padding.symmetric(horizontal=Spacing.SM),
                margin=ft.margin.only(right=Spacing.MD)
            )
        ]
    )

def create_admin_page_layout(page: ft.Page, title: str, content):
    """
    Create an admin page layout without sidebar.

    Args:
        page: The Flet page object
        title: The page title
        content: The page content

    Returns:
        ft.View: The page view
    """
    # Create admin app bar
    app_bar = create_admin_app_bar(page, title)

    # Create content column
    is_mobile = getattr(page, 'is_mobile', False)

    content_column = []

    if isinstance(content, list):
        content_column.extend(content)
    else:
        content_column.append(content)

    # Strict minimal content area
    main_content = ft.Container(
        content=ft.Column(
            content_column,
            expand=True,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            alignment=ft.MainAxisAlignment.START,
            scroll=ft.ScrollMode.AUTO,
            spacing=Spacing.LG
        ),
        expand=True,
        padding=ft.padding.all(Spacing.XL if not is_mobile else Spacing.LG),
        bgcolor=Colors.BACKGROUND
    )

    # Create the layout
    return ft.View(
        page.route,
        [
            app_bar,
            main_content
        ],
        padding=0,
        spacing=0,
        bgcolor=ft.Colors.WHITE
    )
