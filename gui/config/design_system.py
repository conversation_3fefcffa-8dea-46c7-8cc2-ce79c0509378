"""
Strict Modern Design System for Admin Interface
Provides consistent styling, spacing, and components for a clean, professional look.
"""
import flet as ft

class Colors:
    """Strict color palette for professional admin interface"""
    # Primary colors
    PRIMARY = ft.Colors.GREY_900
    PRIMARY_LIGHT = ft.Colors.GREY_800
    PRIMARY_DARK = ft.Colors.BLACK
    
    # Secondary colors
    SECONDARY = ft.Colors.BLUE_600
    SECONDARY_LIGHT = ft.Colors.BLUE_500
    SECONDARY_DARK = ft.Colors.BLUE_700
    
    # Neutral colors
    WHITE = ft.Colors.WHITE
    GREY_50 = ft.Colors.GREY_50
    GREY_100 = ft.Colors.GREY_100
    GREY_200 = ft.Colors.GREY_200
    GREY_300 = ft.Colors.GREY_300
    GREY_400 = ft.Colors.GREY_400
    GREY_500 = ft.Colors.GREY_500
    GREY_600 = ft.Colors.GREY_600
    GREY_700 = ft.Colors.GREY_700
    GREY_800 = ft.Colors.GREY_800
    GREY_900 = ft.Colors.GREY_900
    
    # Status colors
    SUCCESS = ft.Colors.GREEN_600
    WARNING = ft.Colors.ORANGE_600
    ERROR = ft.Colors.RED_600
    INFO = ft.Colors.BLUE_600
    
    # Background colors
    BACKGROUND = ft.Colors.WHITE
    SURFACE = ft.Colors.GREY_50
    CARD = ft.Colors.WHITE
    
    # Text colors
    TEXT_PRIMARY = ft.Colors.GREY_900
    TEXT_SECONDARY = ft.Colors.GREY_600
    TEXT_DISABLED = ft.Colors.GREY_400
    TEXT_ON_PRIMARY = ft.Colors.WHITE

class Typography:
    """Strict typography system"""
    # Font weights
    LIGHT = ft.FontWeight.W_300
    REGULAR = ft.FontWeight.W_400
    MEDIUM = ft.FontWeight.W_500
    SEMIBOLD = ft.FontWeight.W_600
    BOLD = ft.FontWeight.W_700
    
    # Font sizes
    H1 = 32
    H2 = 28
    H3 = 24
    H4 = 20
    H5 = 18
    H6 = 16
    BODY_LARGE = 16
    BODY = 14
    BODY_SMALL = 12
    CAPTION = 11
    OVERLINE = 10

class Spacing:
    """Consistent spacing system"""
    XS = 4
    SM = 8
    MD = 16
    LG = 24
    XL = 32
    XXL = 48
    XXXL = 64

class BorderRadius:
    """Consistent border radius system"""
    NONE = 0
    SM = 4
    MD = 8
    LG = 12
    XL = 16
    ROUND = 50

class Elevation:
    """Shadow system for depth"""
    NONE = None
    SM = ft.BoxShadow(
        spread_radius=0,
        blur_radius=2,
        color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
        offset=ft.Offset(0, 1)
    )
    MD = ft.BoxShadow(
        spread_radius=0,
        blur_radius=4,
        color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
        offset=ft.Offset(0, 2)
    )
    LG = ft.BoxShadow(
        spread_radius=0,
        blur_radius=8,
        color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
        offset=ft.Offset(0, 4)
    )

def create_text(text: str, variant: str = "body", color: str = None, weight: str = None) -> ft.Text:
    """Create consistent text with design system"""
    size_map = {
        "h1": Typography.H1,
        "h2": Typography.H2,
        "h3": Typography.H3,
        "h4": Typography.H4,
        "h5": Typography.H5,
        "h6": Typography.H6,
        "body_large": Typography.BODY_LARGE,
        "body": Typography.BODY,
        "body_small": Typography.BODY_SMALL,
        "caption": Typography.CAPTION,
        "overline": Typography.OVERLINE,
    }
    
    weight_map = {
        "light": Typography.LIGHT,
        "regular": Typography.REGULAR,
        "medium": Typography.MEDIUM,
        "semibold": Typography.SEMIBOLD,
        "bold": Typography.BOLD,
    }
    
    return ft.Text(
        text,
        size=size_map.get(variant, Typography.BODY),
        color=color or Colors.TEXT_PRIMARY,
        weight=weight_map.get(weight, Typography.REGULAR)
    )

def create_card(content, padding: int = None, margin: int = None, elevation: str = "sm") -> ft.Container:
    """Create consistent card component"""
    elevation_map = {
        "none": Elevation.NONE,
        "sm": Elevation.SM,
        "md": Elevation.MD,
        "lg": Elevation.LG,
    }
    
    return ft.Container(
        content=content,
        padding=ft.padding.all(padding or Spacing.MD),
        margin=ft.margin.all(margin or Spacing.SM),
        bgcolor=Colors.CARD,
        border_radius=ft.border_radius.all(BorderRadius.MD),
        shadow=elevation_map.get(elevation, Elevation.SM),
        border=ft.border.all(1, Colors.GREY_200)
    )

def create_button(text: str, on_click=None, variant: str = "primary", size: str = "medium") -> ft.ElevatedButton:
    """Create consistent button component"""
    style_map = {
        "primary": ft.ButtonStyle(
            bgcolor=Colors.PRIMARY,
            color=Colors.TEXT_ON_PRIMARY,
            text_style=ft.TextStyle(weight=Typography.MEDIUM),
            shape=ft.RoundedRectangleBorder(radius=BorderRadius.SM),
        ),
        "secondary": ft.ButtonStyle(
            bgcolor=Colors.SECONDARY,
            color=Colors.TEXT_ON_PRIMARY,
            text_style=ft.TextStyle(weight=Typography.MEDIUM),
            shape=ft.RoundedRectangleBorder(radius=BorderRadius.SM),
        ),
        "outline": ft.ButtonStyle(
            bgcolor=Colors.WHITE,
            color=Colors.PRIMARY,
            text_style=ft.TextStyle(weight=Typography.MEDIUM),
            shape=ft.RoundedRectangleBorder(radius=BorderRadius.SM),
            side=ft.BorderSide(1, Colors.GREY_300),
        ),
    }
    
    padding_map = {
        "small": ft.padding.symmetric(horizontal=Spacing.SM, vertical=Spacing.XS),
        "medium": ft.padding.symmetric(horizontal=Spacing.MD, vertical=Spacing.SM),
        "large": ft.padding.symmetric(horizontal=Spacing.LG, vertical=Spacing.MD),
    }
    
    style = style_map.get(variant, style_map["primary"])
    style.padding = padding_map.get(size, padding_map["medium"])
    
    return ft.ElevatedButton(
        text=text,
        on_click=on_click,
        style=style
    )

def create_input(label: str, hint: str = None, value: str = None, on_change=None) -> ft.TextField:
    """Create consistent input field"""
    return ft.TextField(
        label=label,
        hint_text=hint,
        value=value,
        on_change=on_change,
        border_color=Colors.GREY_300,
        focused_border_color=Colors.SECONDARY,
        label_style=ft.TextStyle(color=Colors.TEXT_SECONDARY, size=Typography.BODY_SMALL),
        text_style=ft.TextStyle(color=Colors.TEXT_PRIMARY, size=Typography.BODY),
        border_radius=BorderRadius.SM,
        content_padding=ft.padding.symmetric(horizontal=Spacing.MD, vertical=Spacing.SM)
    )
